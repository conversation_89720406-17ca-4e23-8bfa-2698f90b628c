#!/usr/bin/env python3
"""
Diagnostic script specifically for the "index -100001 is out of bounds" error
This script helps identify the root cause and provides targeted solutions.
"""

import os
import sys
import torch
import numpy as np
import traceback
import requests
from pathlib import Path

def print_header(title):
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def check_pytorch_installation():
    """Check if PyTorch is properly installed and working"""
    print_header("PYTORCH INSTALLATION CHECK")
    
    try:
        print(f"✅ PyTorch version: {torch.__version__}")
        print(f"✅ Python version: {sys.version}")
        
        # Test basic tensor operations
        test_tensor = torch.randn(10, 10)
        result = test_tensor.sum()
        print(f"✅ Basic tensor operations work: {result.item():.3f}")
        
        # Test CUDA if available
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name()}")
            try:
                cuda_tensor = torch.randn(10, 10).cuda()
                cuda_result = cuda_tensor.sum().cpu()
                print(f"✅ CUDA operations work: {cuda_result.item():.3f}")
            except Exception as e:
                print(f"⚠️  CUDA operations failed: {e}")
        else:
            print("ℹ️  CUDA not available (using CPU)")
            
        return True
        
    except Exception as e:
        print(f"❌ PyTorch check failed: {e}")
        traceback.print_exc()
        return False

def check_model_urls():
    """Check if model URLs are accessible"""
    print_header("MODEL URL ACCESSIBILITY CHECK")
    
    models = {
        "ECCV16": "https://colorizers.s3.us-east-2.amazonaws.com/colorization_release_v2-9b330a0b.pth",
        "SIGGRAPH17": "https://colorizers.s3.us-east-2.amazonaws.com/siggraph17-df00044c.pth"
    }
    
    all_accessible = True
    
    for model_name, url in models.items():
        try:
            print(f"Checking {model_name}...")
            response = requests.head(url, timeout=10)
            
            if response.status_code == 200:
                content_length = response.headers.get('content-length', 'Unknown')
                print(f"✅ {model_name}: Accessible (Size: {content_length} bytes)")
            else:
                print(f"❌ {model_name}: HTTP {response.status_code}")
                all_accessible = False
                
        except requests.RequestException as e:
            print(f"❌ {model_name}: Network error - {e}")
            all_accessible = False
        except Exception as e:
            print(f"❌ {model_name}: Unexpected error - {e}")
            all_accessible = False
    
    return all_accessible

def check_torch_cache():
    """Check torch cache directory and model files"""
    print_header("TORCH CACHE DIRECTORY CHECK")
    
    try:
        cache_dir = torch.hub.get_dir()
        print(f"📁 Torch cache directory: {cache_dir}")
        
        if os.path.exists(cache_dir):
            print(f"✅ Cache directory exists")
            
            # Check checkpoints subdirectory
            checkpoints_dir = os.path.join(cache_dir, "checkpoints")
            if os.path.exists(checkpoints_dir):
                print(f"✅ Checkpoints directory exists")
                
                # List model files
                model_files = list(Path(checkpoints_dir).glob("*.pth"))
                if model_files:
                    print(f"📄 Found {len(model_files)} model files:")
                    for file in model_files:
                        size_mb = file.stat().st_size / (1024 * 1024)
                        print(f"   - {file.name} ({size_mb:.1f} MB)")
                else:
                    print("⚠️  No model files found in cache")
            else:
                print("⚠️  Checkpoints directory doesn't exist")
        else:
            print("⚠️  Cache directory doesn't exist")
            
        return True
        
    except Exception as e:
        print(f"❌ Cache check failed: {e}")
        return False

def test_model_loading():
    """Test loading the colorization models"""
    print_header("MODEL LOADING TEST")
    
    try:
        # Import the models
        sys.path.append('.')
        from models.deep_colorization import eccv16, siggraph17
        
        models_to_test = [
            ("ECCV16", eccv16),
            ("SIGGRAPH17", siggraph17)
        ]
        
        for model_name, model_func in models_to_test:
            print(f"\nTesting {model_name}...")
            
            try:
                # Load model
                print(f"  Loading {model_name}...")
                model = model_func(pretrained=True)
                
                if model is None:
                    print(f"  ❌ {model_name}: Model is None")
                    continue
                
                # Check parameters
                param_count = sum(p.numel() for p in model.parameters())
                print(f"  ✅ {model_name}: Loaded with {param_count:,} parameters")
                
                # Test with dummy input
                print(f"  Testing {model_name} inference...")
                model.eval()
                
                with torch.no_grad():
                    dummy_input = torch.randn(1, 1, 256, 256)
                    try:
                        output = model(dummy_input)
                        
                        if output is None:
                            print(f"  ❌ {model_name}: Output is None")
                        elif output.numel() == 0:
                            print(f"  ❌ {model_name}: Output is empty")
                        elif torch.isnan(output).any():
                            print(f"  ❌ {model_name}: Output contains NaN")
                        elif torch.isinf(output).any():
                            print(f"  ❌ {model_name}: Output contains Inf")
                        else:
                            print(f"  ✅ {model_name}: Inference successful, output shape: {output.shape}")
                            
                    except Exception as e:
                        error_str = str(e)
                        if "-100001" in error_str:
                            print(f"  🚨 {model_name}: FOUND THE -100001 ERROR!")
                            print(f"     Error: {error_str}")
                            print(f"     This indicates corrupted model weights or indexing issue")
                        else:
                            print(f"  ❌ {model_name}: Inference failed - {error_str}")
                        
            except Exception as e:
                error_str = str(e)
                if "-100001" in error_str:
                    print(f"  🚨 {model_name}: FOUND THE -100001 ERROR DURING LOADING!")
                    print(f"     Error: {error_str}")
                else:
                    print(f"  ❌ {model_name}: Loading failed - {error_str}")
                    
        return True
        
    except ImportError as e:
        print(f"❌ Cannot import models: {e}")
        print("Make sure you're running this from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Model loading test failed: {e}")
        traceback.print_exc()
        return False

def provide_solutions():
    """Provide targeted solutions based on findings"""
    print_header("RECOMMENDED SOLUTIONS")
    
    print("Based on the diagnostic results above:")
    print()
    print("🔧 **If you saw the -100001 error:**")
    print("   1. Delete the torch cache directory completely")
    print("   2. Restart Python/Streamlit")
    print("   3. Let the models redownload from scratch")
    print()
    print("🔧 **If model URLs are not accessible:**")
    print("   1. Check your internet connection")
    print("   2. Try using a VPN if you're in a restricted region")
    print("   3. Check if your firewall is blocking the requests")
    print()
    print("🔧 **If PyTorch operations fail:**")
    print("   1. Reinstall PyTorch: pip uninstall torch && pip install torch")
    print("   2. Check for conflicting packages")
    print("   3. Try a different PyTorch version")
    print()
    print("🔧 **Manual cache clearing commands:**")
    cache_dir = torch.hub.get_dir()
    print(f"   - Windows: rmdir /s \"{cache_dir}\"")
    print(f"   - Linux/Mac: rm -rf \"{cache_dir}\"")
    print()
    print("🔧 **If all else fails:**")
    print("   1. Create a fresh Python environment")
    print("   2. Reinstall all dependencies")
    print("   3. Try running on a different machine")

def main():
    """Run all diagnostic tests"""
    print("🔍 DIAGNOSTIC TOOL FOR 'index -100001 is out of bounds' ERROR")
    print("This tool will help identify the root cause of the colorization error.")
    
    # Run all checks
    pytorch_ok = check_pytorch_installation()
    urls_ok = check_model_urls()
    cache_ok = check_torch_cache()
    models_ok = test_model_loading()
    
    # Summary
    print_header("DIAGNOSTIC SUMMARY")
    print(f"PyTorch Installation: {'✅ OK' if pytorch_ok else '❌ FAILED'}")
    print(f"Model URLs Accessible: {'✅ OK' if urls_ok else '❌ FAILED'}")
    print(f"Torch Cache: {'✅ OK' if cache_ok else '❌ FAILED'}")
    print(f"Model Loading: {'✅ OK' if models_ok else '❌ FAILED'}")
    
    if not all([pytorch_ok, urls_ok, cache_ok, models_ok]):
        print("\n⚠️  Issues detected! See solutions below.")
    else:
        print("\n🎉 All checks passed! The -100001 error might be intermittent.")
    
    provide_solutions()

if __name__ == "__main__":
    main()
