name: CI

on: [push, pull_request]

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    name: Do the code respects Python standards?
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'
      - name: Install pre-commit & requirements
        run: |
          pip install pre-commit pylint
          pip install -r requirements.txt
      - name: Run pre-commit
        run: pre-commit run --all-files