import os
import zipfile
import uuid
import tempfile

import streamlit as st
from PIL import Image

from utils import get_model, setup_columns, set_page_config, colorize_image

set_page_config()
col2 = setup_columns()

with col2:
    st.write(
        """
    ## B&W Images Colorizer
    ##### Input a black and white image and get a colorized version of it.
    ###### ➠ If you want to colorize multiple images just upload them all at once.
    ###### ➠ Uploading already colored images won't raise errors but images won't look good.
    ###### ➠ I recommend starting with the first model and then experimenting with the second one."""
    )


def main():
    """
    Main function
    """
    model = st.selectbox(
        "Select Model (Both models have their pros and cons, "
        "I recommend trying both and keeping the best for you task)",
        ["ECCV16", "SIGGRAPH17"],
        index=0,
    )

    try:
        loaded_model = get_model(model)
        st.write(f"Model is now {model}")
    except Exception as e:
        st.error(f"Error loading model: {e}")
        return

    # Ask the user if he wants to see colorization
    display_results = st.checkbox("Display results in real time", value=True)

    # Input for the user to upload images
    uploaded_files = st.file_uploader(
        "Upload your images here...", type=["jpg", "png", "jpeg"], accept_multiple_files=True
    )

    # If the user clicks on the button
    if st.button("Colorize"):
        if not uploaded_files:
            st.warning("Please upload at least one image file", icon="⚠️")
            return
            
        unique_id = str(uuid.uuid4())[:8]
        temp_files = []
        
        try:
            if display_results:
                col1, col2 = st.columns([0.5, 0.5])
                with col1:
                    st.markdown('<p style="text-align: center;">Before</p>', unsafe_allow_html=True)
                with col2:
                    st.markdown('<p style="text-align: center;">After</p>', unsafe_allow_html=True)
            else:
                col1, col2, _ = st.columns(3)

            processed_images = []
            
            for i, file in enumerate(uploaded_files):
                file_extension = os.path.splitext(file.name)[1].lower()
                if file_extension in [".jpg", ".png", ".jpeg"]:
                    try:
                        image = Image.open(file)
                        
                        if display_results:
                            with col1:
                                st.image(image, use_column_width="always")
                            with col2:
                                with st.spinner(f"Colorizing image {i+1}/{len(uploaded_files)}..."):
                                    out_img, new_img = colorize_image(file, loaded_model)
                                    temp_filename = f"IMG_{unique_id}_{i + 1}.jpg"
                                    new_img.save(temp_filename)
                                    temp_files.append(temp_filename)
                                    processed_images.append((out_img, temp_filename))
                                    st.image(out_img, use_column_width="always")
                        else:
                            with st.spinner(f"Colorizing image {i+1}/{len(uploaded_files)}..."):
                                out_img, new_img = colorize_image(file, loaded_model)
                                temp_filename = f"IMG_{unique_id}_{i + 1}.jpg"
                                new_img.save(temp_filename)
                                temp_files.append(temp_filename)
                                processed_images.append((out_img, temp_filename))
                                
                    except Exception as e:
                        st.error(f"Error processing image {file.name}: {e}")
                        continue
                else:
                    st.warning(f"Skipping {file.name}: unsupported format")

            if not processed_images:
                st.error("No images were processed successfully")
                return

            # Handle downloads
            if len(processed_images) > 1:
                # Create a zip file
                zip_filename = f"colorized_images_{unique_id}.zip"
                with zipfile.ZipFile(zip_filename, "w") as zip_file:
                    # Add colorized images to the zip file
                    for i, (_, temp_filename) in enumerate(processed_images):
                        zip_file.write(temp_filename, f"IMG_{i + 1}.jpg")
                
                with col2 if display_results else col1:
                    # Provide the zip file data for download
                    with open(zip_filename, "rb") as f:
                        st.download_button(
                            label="Download Colorized Images",
                            data=f.read(),
                            file_name="colorized_images.zip",
                        )
                temp_files.append(zip_filename)
            else:
                # Single image download
                with col2 if display_results else col1:
                    with open(processed_images[0][1], "rb") as f:
                        st.download_button(
                            label="Download Colorized Image",
                            data=f.read(),
                            file_name="colorized_image.jpg",
                        )

        except Exception as e:
            st.error(f"An error occurred during processing: {e}")
            
        finally:
            # Clean up temporary files
            for temp_file in temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
                except OSError:
                    pass


if __name__ == "__main__":
    main()
    st.markdown(
        "###### Made with :heart: by [Clément Delteil](https://www.linkedin.com/in/clementdelteil/) [![this is an "
        "image link](https://i.imgur.com/thJhzOO.png)](https://www.buymeacoffee.com/clementdelteil)"
    )
