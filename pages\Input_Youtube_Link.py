import os
import time
import uuid
import tempfile

import cv2
import moviepy.editor as mp
import numpy as np
import streamlit as st
from pytube import YouTube
from tqdm import tqdm

from utils import format_time, colorize_frame, colorize_frames_batch, get_model, setup_columns, set_page_config

set_page_config()
col2 = setup_columns()

with col2:
    st.write(
        """
    ## B&W Videos Colorizer
    ##### Input a YouTube black and white video link and get a colorized version of it.
    ###### ➠ This space is using CPU Basic so it might take a while to colorize a video.
    ###### ➠ If you want more models and GPU available please support this space by donating."""
    )


def download_video(link, unique_id):
    """
    Download video from YouTube with unique filename
    """
    try:
        yt = YouTube(link)
        filename = f"video_{unique_id}.mp4"
        video = (
            yt.streams.filter(progressive=True, file_extension="mp4")
            .order_by("resolution")
            .desc()
            .first()
            .download(filename=filename)
        )
        return video
    except Exception as e:
        raise Exception(f"Failed to download video: {str(e)}")


def main():
    """
    Main function
    """
    model = st.selectbox(
        "Select Model (Both models have their pros and cons,"
        "I recommend trying both and keeping the best for you task)",
        ["ECCV16", "SIGGRAPH17"],
        index=0,
    )

    try:
        loaded_model = get_model(model)
        st.write(f"Model is now {model}")
    except Exception as e:
        st.error(f"Error loading model: {e}")
        return

    # Performance options
    col1, col2 = st.columns(2)
    with col1:
        processing_quality = st.selectbox(
            "Processing Quality (affects speed)",
            ["Fast (Lower Quality)", "Balanced", "High Quality (Slower)"],
            index=1
        )
    with col2:
        max_duration = st.slider(
            "Max video duration to process (minutes)", 
            min_value=0.5, 
            max_value=5.0, 
            value=2.0, 
            step=0.5,
            help="Longer videos will be processed with frame skipping"
        )

    link = st.text_input("YouTube Link (The longer the video, the longer the processing time)")
    
    if st.button("Colorize"):
        if not link.strip():
            st.warning("Please enter a YouTube link")
            return
            
        # Validate YouTube URL
        if not ("youtube.com" in link or "youtu.be" in link):
            st.error("Please enter a valid YouTube URL")
            return
            
        unique_id = str(uuid.uuid4())[:8]
        yt_video = None
        audio = None
        video = None
        clip = None
        
        try:
            with st.spinner("Downloading video from YouTube..."):
                yt_video = download_video(link, unique_id)
                
            col1, col2 = st.columns([0.5, 0.5])
            with col1:
                st.markdown('<p style="text-align: center;">Before</p>', unsafe_allow_html=True)
                st.video(yt_video)
                
            with col2:
                st.markdown('<p style="text-align: center;">After</p>', unsafe_allow_html=True)
                
                with st.spinner("Processing video..."):
                    audio = mp.AudioFileClip(yt_video)
                    video = cv2.VideoCapture(yt_video)
                    
                    if not video.isOpened():
                        st.error("Error: Could not open downloaded video file.")
                        return

                    total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
                    fps = video.get(cv2.CAP_PROP_FPS)
                    duration = total_frames / fps if fps > 0 else 0
                    
                    if total_frames == 0:
                        st.error("Error: Downloaded video has no frames or is corrupted.")
                        return
                    
                    # Calculate frame skip for long videos
                    max_duration_seconds = max_duration * 60
                    frame_skip = 1
                    if duration > max_duration_seconds:
                        frame_skip = max(1, int(duration / max_duration_seconds))
                        st.info(f"Video is {duration:.1f}s long. Processing every {frame_skip} frame(s) to stay within {max_duration:.1f} minute limit.")
                        total_frames = total_frames // frame_skip
                    
                    # Adjust batch size based on quality setting
                    if processing_quality == "Fast (Lower Quality)":
                        batch_size = 16
                        chunk_size = 32
                    elif processing_quality == "Balanced":
                        batch_size = 8
                        chunk_size = 16
                    else:  # High Quality
                        batch_size = 4
                        chunk_size = 8

                    # Process video in chunks to save memory
                    output_filename = f"output_{unique_id}.mp4"
                    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
                    
                    # Initialize video writer (we'll get frame size from first frame)
                    out = None
                    
                    progress_bar = st.progress(0)
                    start_time = time.time()
                    time_text = st.text("Time Remaining: ")
                    
                    frames_processed = 0
                    frame_buffer = []
                    frame_counter = 0
                    
                    # Reset video capture to beginning
                    video.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    
                    while frames_processed < total_frames:
                        # Read chunk of frames with frame skipping
                        frame_buffer = []
                        for _ in range(min(chunk_size, total_frames - frames_processed)):
                            ret, frame = video.read()
                            if not ret:
                                break
                            
                            # Apply frame skipping
                            if frame_counter % frame_skip == 0:
                                frame_buffer.append(frame)
                            frame_counter += 1
                        
                        if not frame_buffer:
                            break
                        
                        # Process chunk with batch colorization
                        colorized_chunk = colorize_frames_batch(frame_buffer, loaded_model, batch_size=batch_size)
                        
                        # Initialize video writer with first frame dimensions
                        if out is None and colorized_chunk:
                            frame_size = colorized_chunk[0].shape[:2]
                            out = cv2.VideoWriter(output_filename, fourcc, fps, (frame_size[1], frame_size[0]))
                        
                        # Write colorized frames to output video
                        for colorized_frame in colorized_chunk:
                            frame_bgr = cv2.cvtColor(colorized_frame, cv2.COLOR_RGB2BGR)
                            out.write(frame_bgr)
                        
                        frames_processed += len(colorized_chunk)
                        
                        # Update progress
                        elapsed_time = time.time() - start_time
                        if frames_processed > 0:
                            time_remaining = ((total_frames - frames_processed) / frames_processed) * elapsed_time
                            progress_bar.progress(frames_processed / total_frames)
                            if frames_processed < total_frames:
                                time_text.text(f"Time Remaining: {format_time(time_remaining)}")
                    
                    if out is not None:
                        out.release()
                    else:
                        st.error("No frames were processed successfully.")
                        return
                        
                    progress_bar.empty()
                    time_text.empty()

                    # Convert the output video to a format compatible with Streamlit
                    converted_filename = f"converted_output_{unique_id}.mp4"
                    clip = mp.VideoFileClip(output_filename)
                    clip = clip.set_audio(audio)

                    clip.write_videofile(converted_filename, codec="libx264")

                    # Display the converted video using st.video()
                    st.video(converted_filename)
                    st.balloons()

                    # Add a download button for the colorized video
                    with open(converted_filename, "rb") as f:
                        st.download_button(
                            label="Download Colorized Video",
                            data=f.read(),
                            file_name="colorized_video.mp4",
                        )

        except Exception as e:
            st.error(f"An error occurred: {e}")
            
        finally:
            # Clean up resources
            if video is not None:
                video.release()
            if audio is not None:
                audio.close()
            if clip is not None:
                clip.close()
                
            # Clean up temporary files
            for filename in [yt_video, f"output_{unique_id}.mp4", f"converted_output_{unique_id}.mp4"]:
                if filename and os.path.exists(filename):
                    try:
                        os.unlink(filename)
                    except OSError:
                        pass


if __name__ == "__main__":
    main()
    st.markdown(
        "###### Made with :heart: by [Clément Delteil](https://www.linkedin.com/in/clementdelteil/) [![this is an "
        "image link](https://i.imgur.com/thJhzOO.png)](https://www.buymeacoffee.com/clementdelteil)"
    )
