#!/usr/bin/env python3
"""
Test script to diagnose model loading and colorization issues
"""

import numpy as np
import torch
import cv2
from PIL import Image
import traceback

# Import the models and utilities
from models.deep_colorization import eccv16, siggraph17, load_img, preprocess_img, postprocess_tens

def test_model_loading():
    """Test if models can be loaded successfully"""
    print("=" * 50)
    print("TESTING MODEL LOADING")
    print("=" * 50)
    
    # Test ECCV16 model
    try:
        print("Loading ECCV16 model...")
        model_eccv = eccv16(pretrained=True).eval()
        print("✅ ECCV16 model loaded successfully")
        
        # Test with dummy input
        dummy_input = torch.randn(1, 1, 256, 256)
        with torch.no_grad():
            output = model_eccv(dummy_input)
        print(f"✅ ECCV16 model test passed. Output shape: {output.shape}")
        
    except Exception as e:
        print(f"❌ ECCV16 model failed: {str(e)}")
        traceback.print_exc()
    
    # Test SIGGRAPH17 model
    try:
        print("\nLoading SIGGRAPH17 model...")
        model_siggraph = siggraph17(pretrained=True).eval()
        print("✅ SIGGRAPH17 model loaded successfully")
        
        # Test with dummy input
        dummy_input = torch.randn(1, 1, 256, 256)
        with torch.no_grad():
            output = model_siggraph(dummy_input)
        print(f"✅ SIGGRAPH17 model test passed. Output shape: {output.shape}")
        
    except Exception as e:
        print(f"❌ SIGGRAPH17 model failed: {str(e)}")
        traceback.print_exc()

def test_image_processing():
    """Test image preprocessing and postprocessing"""
    print("\n" + "=" * 50)
    print("TESTING IMAGE PROCESSING")
    print("=" * 50)
    
    try:
        # Create a test image
        test_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
        print(f"Created test image with shape: {test_image.shape}")
        
        # Test preprocessing
        tens_l_orig, tens_l_rs = preprocess_img(test_image, HW=(256, 256))
        print(f"✅ Preprocessing successful. Original tensor shape: {tens_l_orig.shape}, Resized tensor shape: {tens_l_rs.shape}")
        
        # Test with model
        model = eccv16(pretrained=True).eval()
        with torch.no_grad():
            output = model(tens_l_rs)
        print(f"✅ Model inference successful. Output shape: {output.shape}")
        
        # Test postprocessing
        result = postprocess_tens(tens_l_orig, output)
        print(f"✅ Postprocessing successful. Result shape: {result.shape}")
        
        # Check result validity
        if result is not None and result.size > 0:
            print(f"✅ Result is valid. Min: {result.min():.3f}, Max: {result.max():.3f}")
        else:
            print("❌ Result is invalid (None or empty)")
            
    except Exception as e:
        print(f"❌ Image processing failed: {str(e)}")
        traceback.print_exc()

def test_batch_processing():
    """Test batch processing of multiple frames"""
    print("\n" + "=" * 50)
    print("TESTING BATCH PROCESSING")
    print("=" * 50)
    
    try:
        # Create test frames
        frames = []
        for i in range(3):
            frame = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
            frames.append(frame)
        print(f"Created {len(frames)} test frames")
        
        # Load model
        model = eccv16(pretrained=True).eval()
        
        # Test batch processing
        batch_tensors = []
        orig_tensors = []
        
        for frame in frames:
            tens_l_orig, tens_l_rs = preprocess_img(frame, HW=(256, 256))
            batch_tensors.append(tens_l_rs)
            orig_tensors.append(tens_l_orig)
        
        if batch_tensors:
            batch_tensor = torch.cat(batch_tensors, dim=0)
            print(f"✅ Batch tensor created with shape: {batch_tensor.shape}")
            
            with torch.no_grad():
                batch_output = model(batch_tensor)
            print(f"✅ Batch inference successful. Output shape: {batch_output.shape}")
            
            # Test postprocessing each frame
            for j, (tens_l_orig, out_ab) in enumerate(zip(orig_tensors, batch_output)):
                result = postprocess_tens(tens_l_orig, out_ab.unsqueeze(0))
                if result is not None and result.size > 0:
                    print(f"✅ Frame {j} postprocessed successfully. Shape: {result.shape}")
                else:
                    print(f"❌ Frame {j} postprocessing failed")
        
    except Exception as e:
        print(f"❌ Batch processing failed: {str(e)}")
        traceback.print_exc()

def test_edge_cases():
    """Test edge cases that might cause the -100001 error"""
    print("\n" + "=" * 50)
    print("TESTING EDGE CASES")
    print("=" * 50)
    
    # Test empty arrays
    try:
        print("Testing empty array...")
        empty_array = np.array([])
        if empty_array.size == 0:
            print("✅ Empty array detected correctly")
    except Exception as e:
        print(f"❌ Empty array test failed: {str(e)}")
    
    # Test invalid shapes
    try:
        print("Testing invalid image shapes...")
        invalid_shapes = [
            np.zeros((0, 0, 3)),  # Zero dimensions
            np.zeros((256, 256)),  # Missing channel dimension
            np.zeros((256, 256, 1)),  # Single channel
            np.zeros((256, 256, 4)),  # RGBA
        ]
        
        for i, invalid_img in enumerate(invalid_shapes):
            print(f"  Testing shape {invalid_img.shape}...")
            try:
                if invalid_img.size == 0:
                    print(f"    ✅ Empty image {i} detected correctly")
                elif len(invalid_img.shape) != 3 or invalid_img.shape[2] != 3:
                    print(f"    ✅ Invalid shape {i} detected correctly")
                else:
                    tens_l_orig, tens_l_rs = preprocess_img(invalid_img, HW=(256, 256))
                    print(f"    ⚠️  Shape {i} processed (might be valid)")
            except Exception as e:
                print(f"    ✅ Shape {i} correctly rejected: {str(e)}")
                
    except Exception as e:
        print(f"❌ Edge case testing failed: {str(e)}")

if __name__ == "__main__":
    print("Starting model and processing diagnostics...")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name()}")
    
    test_model_loading()
    test_image_processing()
    test_batch_processing()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("DIAGNOSTICS COMPLETE")
    print("=" * 50)
