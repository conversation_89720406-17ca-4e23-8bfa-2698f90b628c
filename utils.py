import cv2
import numpy as np
import requests
import streamlit as st
import torch
from PIL import Image
from streamlit_lottie import st_lottie

from models.deep_colorization import eccv16
from models.deep_colorization import siggraph17
from models.deep_colorization import postprocess_tens, preprocess_img, load_img
import os


def set_page_config():
    """
    Sets up the page config.
    """
    st.set_page_config(page_title="Image & Video Colorizer", page_icon="🎨", layout="wide")


def load_model():
    """
    Loads the default model.
    """
    return eccv16(pretrained=True).eval()


def clear_model_cache():
    """
    Clear the model cache if there are issues
    """
    try:
        st.cache_resource.clear()
        st.success("Model cache cleared. Please reload the page.")
    except Exception as e:
        st.error(f"Failed to clear cache: {str(e)}")


def check_model_files():
    """
    Check if model files exist and are valid in the torch cache
    """
    try:
        import torch.utils.model_zoo as model_zoo
        import hashlib
        import requests

        st.info("🔍 Checking model files...")

        # Model URLs and expected hashes
        models = {
            "ECCV16": {
                "url": "https://colorizers.s3.us-east-2.amazonaws.com/colorization_release_v2-9b330a0b.pth",
                "hash": "9b330a0b"
            },
            "SIGGRAPH17": {
                "url": "https://colorizers.s3.us-east-2.amazonaws.com/siggraph17-df00044c.pth",
                "hash": "df00044c"
            }
        }

        for model_name, info in models.items():
            try:
                # Check if we can reach the URL
                response = requests.head(info["url"], timeout=10)
                if response.status_code == 200:
                    st.success(f"✅ {model_name} model URL is accessible")
                else:
                    st.error(f"❌ {model_name} model URL returned status {response.status_code}")

                # Try to load the model state dict to verify integrity
                try:
                    state_dict = model_zoo.load_url(info["url"], map_location="cpu", check_hash=True)
                    if state_dict and len(state_dict) > 0:
                        st.success(f"✅ {model_name} model file is valid ({len(state_dict)} parameters)")
                    else:
                        st.error(f"❌ {model_name} model file is empty or corrupted")
                except Exception as e:
                    st.error(f"❌ {model_name} model file validation failed: {str(e)}")

            except requests.RequestException as e:
                st.error(f"❌ Cannot reach {model_name} model URL: {str(e)}")
            except Exception as e:
                st.error(f"❌ {model_name} model check failed: {str(e)}")

        return True

    except Exception as e:
        st.error(f"❌ Model check failed: {str(e)}")
        return False


def force_redownload_models():
    """
    Force redownload of model files by clearing torch cache
    """
    try:
        import torch
        import shutil

        # Clear Streamlit cache first
        st.cache_resource.clear()

        # Try to clear torch hub cache
        torch_cache_dir = torch.hub.get_dir()
        if os.path.exists(torch_cache_dir):
            st.info(f"Clearing torch cache at: {torch_cache_dir}")
            try:
                shutil.rmtree(torch_cache_dir)
                st.success("✅ Torch cache cleared")
            except Exception as e:
                st.warning(f"Could not clear torch cache: {str(e)}")

        # Also try to clear the model_zoo cache
        try:
            import torch.utils.model_zoo as model_zoo
            # The model_zoo uses torch.hub._get_cache_dir() internally
            cache_dir = os.path.join(torch.hub.get_dir(), "checkpoints")
            if os.path.exists(cache_dir):
                shutil.rmtree(cache_dir)
                st.success("✅ Model zoo cache cleared")
        except Exception as e:
            st.warning(f"Could not clear model zoo cache: {str(e)}")

        st.success("🔄 Model caches cleared. Please reload the page and try again.")

    except Exception as e:
        st.error(f"❌ Failed to clear model caches: {str(e)}")


def validate_tensor_operations():
    """
    Test basic tensor operations to ensure PyTorch is working correctly
    """
    try:
        st.info("🧪 Testing tensor operations...")

        # Test basic tensor creation
        test_tensor = torch.randn(1, 1, 256, 256)
        if test_tensor.numel() == 0:
            raise RuntimeError("Cannot create tensors")
        st.success("✅ Basic tensor creation works")

        # Test tensor operations
        result = test_tensor * 2.0
        if result.numel() != test_tensor.numel():
            raise RuntimeError("Tensor operations are corrupted")
        st.success("✅ Tensor operations work")

        # Test device operations
        if torch.cuda.is_available():
            try:
                cuda_tensor = test_tensor.cuda()
                cpu_tensor = cuda_tensor.cpu()
                st.success("✅ GPU/CPU tensor transfers work")
            except Exception as e:
                st.warning(f"⚠️ GPU operations failed: {str(e)}")

        return True

    except Exception as e:
        st.error(f"❌ Tensor operations test failed: {str(e)}")
        return False


def setup_columns():
    """
    Sets up the columns.
    """
    col1, col2 = st.columns([1, 3])
    lottie = load_lottieurl("https://assets5.lottiefiles.com/packages/lf20_RHdEuzVfEL.json")
    with col1:
        st_lottie(lottie)
    return col2


# Define a function that we can use to load lottie files from a link.
@st.cache_data()
def load_lottieurl(url: str):
    """
    Load lottieurl image
    """
    try:
        r = requests.get(url, timeout=10)  # Timeout set to 10 seconds
        r.raise_for_status()  # This will raise an exception for HTTP errors
        return r.json()
    except requests.RequestException as e:
        print(f"Request failed: {e}")
        return None


@st.cache_resource()
def get_model(model_name, retry_count=0):
    """
    Load and cache model with robust error handling for the -100001 error
    """
    max_retries = 2

    try:
        st.info(f"📥 Loading {model_name} model... (Attempt {retry_count + 1}/{max_retries + 1})")

        # First, validate that PyTorch is working correctly
        if retry_count == 0:
            if not validate_tensor_operations():
                raise RuntimeError("PyTorch tensor operations are not working correctly")

        # Load the model with explicit error handling
        model = None
        try:
            if model_name == "ECCV16":
                model = eccv16(pretrained=True).eval()
            elif model_name == "SIGGRAPH17":
                model = siggraph17(pretrained=True).eval()
            else:
                raise ValueError(f"Unknown model: {model_name}")

        except Exception as e:
            error_msg = str(e).lower()
            if "index" in error_msg and "-100001" in error_msg:
                st.error("🚨 Detected the '-100001 index error' - this indicates corrupted model files!")
                st.error("The model files may have been partially downloaded or corrupted.")
                if retry_count < max_retries:
                    st.warning("Attempting to clear cache and retry...")
                    force_redownload_models()
                    return get_model(model_name, retry_count + 1)
                else:
                    raise RuntimeError(f"Model {model_name} consistently fails with index error after {max_retries} retries")
            else:
                raise e

        # Verify model loaded correctly
        if model is None:
            raise RuntimeError(f"Failed to load {model_name} model - returned None")

        # Verify model parameters exist and are valid
        param_count = sum(p.numel() for p in model.parameters())
        if param_count == 0:
            raise RuntimeError(f"Model {model_name} has no parameters - likely corrupted")
        st.info(f"📊 Model has {param_count:,} parameters")

        # Test model with multiple dummy inputs to catch edge cases
        try:
            test_inputs = [
                torch.randn(1, 1, 256, 256),  # Standard input
                torch.zeros(1, 1, 256, 256),  # All zeros
                torch.ones(1, 1, 256, 256),   # All ones
            ]

            for i, dummy_input in enumerate(test_inputs):
                with torch.no_grad():
                    test_output = model(dummy_input)

                if test_output is None:
                    raise RuntimeError(f"Model produces None output for test input {i}")
                if test_output.numel() == 0:
                    raise RuntimeError(f"Model produces empty output for test input {i}")
                if torch.isnan(test_output).any():
                    raise RuntimeError(f"Model produces NaN values for test input {i}")
                if torch.isinf(test_output).any():
                    raise RuntimeError(f"Model produces infinite values for test input {i}")

            st.success(f"✅ Model validation passed with output shape: {test_output.shape}")

        except Exception as e:
            error_msg = str(e).lower()
            if "index" in error_msg and ("-100001" in error_msg or "out of bounds" in error_msg):
                st.error("🚨 Model validation failed with index error!")
                if retry_count < max_retries:
                    st.warning("Clearing cache and retrying...")
                    force_redownload_models()
                    return get_model(model_name, retry_count + 1)
                else:
                    raise RuntimeError(f"Model {model_name} validation consistently fails after {max_retries} retries")
            else:
                st.error(f"Model validation failed: {str(e)}")
                raise RuntimeError(f"Model {model_name} failed validation test: {str(e)}")

        # Move to GPU if available
        if torch.cuda.is_available():
            try:
                model = model.cuda()
                st.success("🚀 GPU detected! Using GPU acceleration for faster processing.")
            except Exception as e:
                st.warning(f"Failed to move model to GPU: {str(e)}. Using CPU instead.")
        else:
            st.info("💻 Using CPU processing. Consider using a GPU for faster results.")

        st.success(f"🎉 {model_name} model loaded and validated successfully!")
        return model

    except Exception as e:
        st.error(f"❌ Failed to load {model_name} model: {str(e)}")

        # Provide specific guidance based on error type
        error_msg = str(e).lower()
        if "index" in error_msg and "-100001" in error_msg:
            st.error("🔧 **This is the '-100001 index error'**")
            st.error("**Solution:** Use the 'Force Redownload Models' button below and reload the page.")
        elif "connection" in error_msg or "timeout" in error_msg:
            st.error("🌐 **Network issue detected**")
            st.error("**Solution:** Check your internet connection and try again.")
        elif "memory" in error_msg or "cuda" in error_msg:
            st.error("💾 **Memory or GPU issue detected**")
            st.error("**Solution:** Try restarting the application or use CPU mode.")
        else:
            st.error("**Solution:** Try the troubleshooting options below.")

        raise e


def format_time(seconds: float) -> str:
    """Formats time in seconds to a human readable format"""
    if seconds < 60:
        return f"{int(seconds)} seconds"
    if seconds < 3600:
        minutes = seconds // 60
        seconds %= 60
        return f"{minutes} minutes and {int(seconds)} seconds"
    if seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds %= 60
        return f"{hours} hours, {minutes} minutes, and {int(seconds)} seconds"

    days = seconds // 86400
    hours = (seconds % 86400) // 3600
    minutes = (seconds % 3600) // 60
    seconds %= 60
    return f"{days} days, {hours} hours, {minutes} minutes, and {int(seconds)} seconds"


# Function to colorize video frames
def colorize_frame(frame, colorizer) -> np.ndarray:
    """
    Colorize frame with error handling
    """
    try:
        if frame is None or frame.size == 0:
            raise ValueError("Empty or invalid frame provided")

        # Ensure frame has the right shape and type
        if len(frame.shape) != 3 or frame.shape[2] != 3:
            raise ValueError(f"Frame must be RGB with shape (H, W, 3), got {frame.shape}")

        tens_l_orig, tens_l_rs = preprocess_img(frame, HW=(256, 256))

        # Check if tensors are valid
        if tens_l_orig.numel() == 0 or tens_l_rs.numel() == 0:
            raise ValueError("Preprocessing resulted in empty tensors")

        # Move to same device as model
        device = next(colorizer.parameters()).device
        tens_l_rs = tens_l_rs.to(device)

        with torch.no_grad():
            output = colorizer(tens_l_rs).cpu()

        if output.numel() == 0:
            raise ValueError("Model produced empty output")

        result = postprocess_tens(tens_l_orig, output)

        if result is None or result.size == 0:
            raise ValueError("Postprocessing failed")

        return result

    except Exception as e:
        st.error(f"Error in colorize_frame: {str(e)}")
        # Return original frame converted to RGB if colorization fails
        if frame is not None and frame.size > 0:
            return frame.astype(np.float32) / 255.0
        else:
            # Return a black frame as fallback
            return np.zeros((256, 256, 3), dtype=np.float32)


def colorize_frames_batch(frames, colorizer, batch_size=8):
    """
    Colorize multiple frames in batches for better performance with error handling
    """
    colorized_frames = []

    if not frames:
        st.warning("No frames provided for batch colorization")
        return colorized_frames

    # Set model to evaluation mode and disable gradients for faster inference
    colorizer.eval()

    try:
        for i in range(0, len(frames), batch_size):
            batch = frames[i:i + batch_size]
            batch_tensors = []
            orig_tensors = []
            valid_frames = []

            # Preprocess batch with validation
            for frame_idx, frame in enumerate(batch):
                try:
                    if frame is None or frame.size == 0:
                        st.warning(f"Skipping empty frame at index {i + frame_idx}")
                        continue

                    # Ensure frame has the right shape
                    if len(frame.shape) != 3 or frame.shape[2] != 3:
                        st.warning(f"Skipping frame with invalid shape {frame.shape} at index {i + frame_idx}")
                        continue

                    tens_l_orig, tens_l_rs = preprocess_img(frame, HW=(256, 256))

                    if tens_l_orig.numel() == 0 or tens_l_rs.numel() == 0:
                        st.warning(f"Skipping frame with empty tensors at index {i + frame_idx}")
                        continue

                    batch_tensors.append(tens_l_rs)
                    orig_tensors.append(tens_l_orig)
                    valid_frames.append(frame)

                except Exception as e:
                    st.warning(f"Error preprocessing frame {i + frame_idx}: {str(e)}")
                    continue

            # Process valid frames if any
            if batch_tensors and orig_tensors:
                try:
                    batch_tensor = torch.cat(batch_tensors, dim=0)

                    # Process batch through model with optimizations
                    with torch.no_grad():
                        # Move to GPU if available for faster processing
                        device = next(colorizer.parameters()).device
                        batch_tensor = batch_tensor.to(device)
                        batch_output = colorizer(batch_tensor).cpu()

                    # Postprocess each frame in batch
                    for j, (tens_l_orig, out_ab, original_frame) in enumerate(zip(orig_tensors, batch_output, valid_frames)):
                        try:
                            if out_ab.numel() == 0:
                                st.warning(f"Model produced empty output for frame {i + j}")
                                # Use original frame as fallback
                                colorized_frames.append((original_frame).astype(np.uint8))
                                continue

                            colorized = postprocess_tens(tens_l_orig, out_ab.unsqueeze(0))

                            if colorized is None or colorized.size == 0:
                                st.warning(f"Postprocessing failed for frame {i + j}")
                                # Use original frame as fallback
                                colorized_frames.append((original_frame).astype(np.uint8))
                                continue

                            colorized_frames.append((colorized * 255).astype(np.uint8))

                        except Exception as e:
                            st.warning(f"Error postprocessing frame {i + j}: {str(e)}")
                            # Use original frame as fallback
                            colorized_frames.append((original_frame).astype(np.uint8))
                            continue

                except Exception as e:
                    st.error(f"Error processing batch starting at frame {i}: {str(e)}")
                    # Add original frames as fallback
                    for frame in valid_frames:
                        colorized_frames.append((frame).astype(np.uint8))
            else:
                st.warning(f"No valid frames in batch starting at index {i}")

    except Exception as e:
        st.error(f"Critical error in batch colorization: {str(e)}")
        # Return original frames as fallback
        for frame in frames:
            if frame is not None and frame.size > 0:
                colorized_frames.append((frame).astype(np.uint8))

    return colorized_frames


def get_video_info(video_path):
    """
    Get video information efficiently
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return None
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    duration = frame_count / fps if fps > 0 else 0
    
    cap.release()
    
    return {
        'fps': fps,
        'frame_count': frame_count,
        'width': width,
        'height': height,
        'duration': duration
    }


def colorize_image(file, loaded_model):
    """
    Colorize image with error handling
    """
    try:
        img = load_img(file)

        if img is None or img.size == 0:
            raise ValueError("Failed to load image or image is empty")

        # Handle different image formats
        if len(img.shape) == 2:
            # Grayscale image - convert to RGB
            img = np.stack([img] * 3, axis=-1)
        elif len(img.shape) == 3:
            if img.shape[2] == 4:
                # RGBA image - discard alpha channel
                img = img[:, :, :3]
            elif img.shape[2] != 3:
                raise ValueError(f"Unsupported image format with {img.shape[2]} channels")
        else:
            raise ValueError(f"Unsupported image shape: {img.shape}")

        tens_l_orig, tens_l_rs = preprocess_img(img, HW=(256, 256))

        if tens_l_orig.numel() == 0 or tens_l_rs.numel() == 0:
            raise ValueError("Image preprocessing resulted in empty tensors")

        # Move to same device as model
        device = next(loaded_model.parameters()).device
        tens_l_rs = tens_l_rs.to(device)

        with torch.no_grad():
            model_output = loaded_model(tens_l_rs).cpu()

        if model_output.numel() == 0:
            raise ValueError("Model produced empty output")

        out_img = postprocess_tens(tens_l_orig, model_output)

        if out_img is None or out_img.size == 0:
            raise ValueError("Image postprocessing failed")

        # Ensure output is in valid range
        out_img = np.clip(out_img, 0, 1)
        new_img = Image.fromarray((out_img * 255).astype(np.uint8))

        return out_img, new_img

    except Exception as e:
        st.error(f"Error colorizing image: {str(e)}")
        # Return original image as fallback
        try:
            fallback_img = load_img(file)
            if fallback_img is not None:
                if len(fallback_img.shape) == 2:
                    fallback_img = np.stack([fallback_img] * 3, axis=-1)
                elif fallback_img.shape[2] == 4:
                    fallback_img = fallback_img[:, :, :3]

                fallback_img = fallback_img.astype(np.float32) / 255.0
                fallback_pil = Image.fromarray((fallback_img * 255).astype(np.uint8))
                return fallback_img, fallback_pil
        except:
            pass

        # Final fallback - create a black image
        black_img = np.zeros((256, 256, 3), dtype=np.float32)
        black_pil = Image.fromarray((black_img * 255).astype(np.uint8))
        return black_img, black_pil
