import cv2
import numpy as np
import requests
import streamlit as st
import torch
from PIL import Image
from streamlit_lottie import st_lottie

from models.deep_colorization import eccv16
from models.deep_colorization import siggraph17
from models.deep_colorization import postprocess_tens, preprocess_img, load_img
import os


def set_page_config():
    """
    Sets up the page config.
    """
    st.set_page_config(page_title="Image & Video Colorizer", page_icon="🎨", layout="wide")


def load_model():
    """
    Loads the default model.
    """
    return eccv16(pretrained=True).eval()


def clear_model_cache():
    """
    Clear the model cache if there are issues
    """
    try:
        st.cache_resource.clear()
        st.success("Model cache cleared. Please reload the page.")
    except Exception as e:
        st.error(f"Failed to clear cache: {str(e)}")


def check_model_files():
    """
    Check if model files exist in the torch cache
    """
    try:
        import torch.utils.model_zoo as model_zoo
        # Try to check if models are accessible
        eccv_url = "https://colorizers.s3.us-east-2.amazonaws.com/colorization_release_v2-9b330a0b.pth"
        siggraph_url = "https://colorizers.s3.us-east-2.amazonaws.com/siggraph17-df00044c.pth"

        st.info("Checking model availability...")
        return True
    except Exception as e:
        st.warning(f"Model check failed: {str(e)}")
        return False


def setup_columns():
    """
    Sets up the columns.
    """
    col1, col2 = st.columns([1, 3])
    lottie = load_lottieurl("https://assets5.lottiefiles.com/packages/lf20_RHdEuzVfEL.json")
    with col1:
        st_lottie(lottie)
    return col2


# Define a function that we can use to load lottie files from a link.
@st.cache_data()
def load_lottieurl(url: str):
    """
    Load lottieurl image
    """
    try:
        r = requests.get(url, timeout=10)  # Timeout set to 10 seconds
        r.raise_for_status()  # This will raise an exception for HTTP errors
        return r.json()
    except requests.RequestException as e:
        print(f"Request failed: {e}")
        return None


@st.cache_resource()
def get_model(model_name):
    """
    Load and cache model based on name with GPU support and error handling
    """
    try:
        st.info(f"Loading {model_name} model... This may take a few minutes on first run.")

        if model_name == "ECCV16":
            model = eccv16(pretrained=True).eval()
        elif model_name == "SIGGRAPH17":
            model = siggraph17(pretrained=True).eval()
        else:
            raise ValueError(f"Unknown model: {model_name}")

        # Verify model loaded correctly
        if model is None:
            raise RuntimeError(f"Failed to load {model_name} model")

        # Test model with a dummy input
        try:
            dummy_input = torch.randn(1, 1, 256, 256)
            with torch.no_grad():
                test_output = model(dummy_input)
            if test_output is None or test_output.numel() == 0:
                raise RuntimeError(f"Model {model_name} produces empty output")
            st.info(f"Model test passed. Output shape: {test_output.shape}")
        except Exception as e:
            st.error(f"Model test failed: {str(e)}")
            st.error("This indicates the model may not have downloaded correctly or there's a compatibility issue.")
            raise RuntimeError(f"Model {model_name} failed validation test")

        # Move to GPU if available
        if torch.cuda.is_available():
            try:
                model = model.cuda()
                st.success("🚀 GPU detected! Using GPU acceleration for faster processing.")
            except Exception as e:
                st.warning(f"Failed to move model to GPU: {str(e)}. Using CPU instead.")
        else:
            st.info("💻 Using CPU processing. Consider using a GPU for faster results.")

        st.success(f"✅ {model_name} model loaded successfully!")
        return model

    except Exception as e:
        st.error(f"❌ Failed to load {model_name} model: {str(e)}")
        st.error("Please check your internet connection and try again. The model files need to be downloaded on first use.")
        raise e


def format_time(seconds: float) -> str:
    """Formats time in seconds to a human readable format"""
    if seconds < 60:
        return f"{int(seconds)} seconds"
    if seconds < 3600:
        minutes = seconds // 60
        seconds %= 60
        return f"{minutes} minutes and {int(seconds)} seconds"
    if seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds %= 60
        return f"{hours} hours, {minutes} minutes, and {int(seconds)} seconds"

    days = seconds // 86400
    hours = (seconds % 86400) // 3600
    minutes = (seconds % 3600) // 60
    seconds %= 60
    return f"{days} days, {hours} hours, {minutes} minutes, and {int(seconds)} seconds"


# Function to colorize video frames
def colorize_frame(frame, colorizer) -> np.ndarray:
    """
    Colorize frame with error handling
    """
    try:
        if frame is None or frame.size == 0:
            raise ValueError("Empty or invalid frame provided")

        # Ensure frame has the right shape and type
        if len(frame.shape) != 3 or frame.shape[2] != 3:
            raise ValueError(f"Frame must be RGB with shape (H, W, 3), got {frame.shape}")

        tens_l_orig, tens_l_rs = preprocess_img(frame, HW=(256, 256))

        # Check if tensors are valid
        if tens_l_orig.numel() == 0 or tens_l_rs.numel() == 0:
            raise ValueError("Preprocessing resulted in empty tensors")

        # Move to same device as model
        device = next(colorizer.parameters()).device
        tens_l_rs = tens_l_rs.to(device)

        with torch.no_grad():
            output = colorizer(tens_l_rs).cpu()

        if output.numel() == 0:
            raise ValueError("Model produced empty output")

        result = postprocess_tens(tens_l_orig, output)

        if result is None or result.size == 0:
            raise ValueError("Postprocessing failed")

        return result

    except Exception as e:
        st.error(f"Error in colorize_frame: {str(e)}")
        # Return original frame converted to RGB if colorization fails
        if frame is not None and frame.size > 0:
            return frame.astype(np.float32) / 255.0
        else:
            # Return a black frame as fallback
            return np.zeros((256, 256, 3), dtype=np.float32)


def colorize_frames_batch(frames, colorizer, batch_size=8):
    """
    Colorize multiple frames in batches for better performance with error handling
    """
    colorized_frames = []

    if not frames:
        st.warning("No frames provided for batch colorization")
        return colorized_frames

    # Set model to evaluation mode and disable gradients for faster inference
    colorizer.eval()

    try:
        for i in range(0, len(frames), batch_size):
            batch = frames[i:i + batch_size]
            batch_tensors = []
            orig_tensors = []
            valid_frames = []

            # Preprocess batch with validation
            for frame_idx, frame in enumerate(batch):
                try:
                    if frame is None or frame.size == 0:
                        st.warning(f"Skipping empty frame at index {i + frame_idx}")
                        continue

                    # Ensure frame has the right shape
                    if len(frame.shape) != 3 or frame.shape[2] != 3:
                        st.warning(f"Skipping frame with invalid shape {frame.shape} at index {i + frame_idx}")
                        continue

                    tens_l_orig, tens_l_rs = preprocess_img(frame, HW=(256, 256))

                    if tens_l_orig.numel() == 0 or tens_l_rs.numel() == 0:
                        st.warning(f"Skipping frame with empty tensors at index {i + frame_idx}")
                        continue

                    batch_tensors.append(tens_l_rs)
                    orig_tensors.append(tens_l_orig)
                    valid_frames.append(frame)

                except Exception as e:
                    st.warning(f"Error preprocessing frame {i + frame_idx}: {str(e)}")
                    continue

            # Process valid frames if any
            if batch_tensors and orig_tensors:
                try:
                    batch_tensor = torch.cat(batch_tensors, dim=0)

                    # Process batch through model with optimizations
                    with torch.no_grad():
                        # Move to GPU if available for faster processing
                        device = next(colorizer.parameters()).device
                        batch_tensor = batch_tensor.to(device)
                        batch_output = colorizer(batch_tensor).cpu()

                    # Postprocess each frame in batch
                    for j, (tens_l_orig, out_ab, original_frame) in enumerate(zip(orig_tensors, batch_output, valid_frames)):
                        try:
                            if out_ab.numel() == 0:
                                st.warning(f"Model produced empty output for frame {i + j}")
                                # Use original frame as fallback
                                colorized_frames.append((original_frame).astype(np.uint8))
                                continue

                            colorized = postprocess_tens(tens_l_orig, out_ab.unsqueeze(0))

                            if colorized is None or colorized.size == 0:
                                st.warning(f"Postprocessing failed for frame {i + j}")
                                # Use original frame as fallback
                                colorized_frames.append((original_frame).astype(np.uint8))
                                continue

                            colorized_frames.append((colorized * 255).astype(np.uint8))

                        except Exception as e:
                            st.warning(f"Error postprocessing frame {i + j}: {str(e)}")
                            # Use original frame as fallback
                            colorized_frames.append((original_frame).astype(np.uint8))
                            continue

                except Exception as e:
                    st.error(f"Error processing batch starting at frame {i}: {str(e)}")
                    # Add original frames as fallback
                    for frame in valid_frames:
                        colorized_frames.append((frame).astype(np.uint8))
            else:
                st.warning(f"No valid frames in batch starting at index {i}")

    except Exception as e:
        st.error(f"Critical error in batch colorization: {str(e)}")
        # Return original frames as fallback
        for frame in frames:
            if frame is not None and frame.size > 0:
                colorized_frames.append((frame).astype(np.uint8))

    return colorized_frames


def get_video_info(video_path):
    """
    Get video information efficiently
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return None
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    duration = frame_count / fps if fps > 0 else 0
    
    cap.release()
    
    return {
        'fps': fps,
        'frame_count': frame_count,
        'width': width,
        'height': height,
        'duration': duration
    }


def colorize_image(file, loaded_model):
    """
    Colorize image with error handling
    """
    try:
        img = load_img(file)

        if img is None or img.size == 0:
            raise ValueError("Failed to load image or image is empty")

        # Handle different image formats
        if len(img.shape) == 2:
            # Grayscale image - convert to RGB
            img = np.stack([img] * 3, axis=-1)
        elif len(img.shape) == 3:
            if img.shape[2] == 4:
                # RGBA image - discard alpha channel
                img = img[:, :, :3]
            elif img.shape[2] != 3:
                raise ValueError(f"Unsupported image format with {img.shape[2]} channels")
        else:
            raise ValueError(f"Unsupported image shape: {img.shape}")

        tens_l_orig, tens_l_rs = preprocess_img(img, HW=(256, 256))

        if tens_l_orig.numel() == 0 or tens_l_rs.numel() == 0:
            raise ValueError("Image preprocessing resulted in empty tensors")

        # Move to same device as model
        device = next(loaded_model.parameters()).device
        tens_l_rs = tens_l_rs.to(device)

        with torch.no_grad():
            model_output = loaded_model(tens_l_rs).cpu()

        if model_output.numel() == 0:
            raise ValueError("Model produced empty output")

        out_img = postprocess_tens(tens_l_orig, model_output)

        if out_img is None or out_img.size == 0:
            raise ValueError("Image postprocessing failed")

        # Ensure output is in valid range
        out_img = np.clip(out_img, 0, 1)
        new_img = Image.fromarray((out_img * 255).astype(np.uint8))

        return out_img, new_img

    except Exception as e:
        st.error(f"Error colorizing image: {str(e)}")
        # Return original image as fallback
        try:
            fallback_img = load_img(file)
            if fallback_img is not None:
                if len(fallback_img.shape) == 2:
                    fallback_img = np.stack([fallback_img] * 3, axis=-1)
                elif fallback_img.shape[2] == 4:
                    fallback_img = fallback_img[:, :, :3]

                fallback_img = fallback_img.astype(np.float32) / 255.0
                fallback_pil = Image.fromarray((fallback_img * 255).astype(np.uint8))
                return fallback_img, fallback_pil
        except:
            pass

        # Final fallback - create a black image
        black_img = np.zeros((256, 256, 3), dtype=np.float32)
        black_pil = Image.fromarray((black_img * 255).astype(np.uint8))
        return black_img, black_pil
