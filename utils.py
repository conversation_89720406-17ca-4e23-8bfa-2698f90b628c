import cv2
import numpy as np
import requests
import streamlit as st
import torch
from PIL import Image
from streamlit_lottie import st_lottie

from models.deep_colorization import eccv16
from models.deep_colorization import siggraph17
from models.deep_colorization import postprocess_tens, preprocess_img, load_img


def set_page_config():
    """
    Sets up the page config.
    """
    st.set_page_config(page_title="Image & Video Colorizer", page_icon="🎨", layout="wide")


def load_model():
    """
    Loads the default model.
    """
    return eccv16(pretrained=True).eval()


def setup_columns():
    """
    Sets up the columns.
    """
    col1, col2 = st.columns([1, 3])
    lottie = load_lottieurl("https://assets5.lottiefiles.com/packages/lf20_RHdEuzVfEL.json")
    with col1:
        st_lottie(lottie)
    return col2


# Define a function that we can use to load lottie files from a link.
@st.cache_data()
def load_lottieurl(url: str):
    """
    Load lottieurl image
    """
    try:
        r = requests.get(url, timeout=10)  # Timeout set to 10 seconds
        r.raise_for_status()  # This will raise an exception for HTTP errors
        return r.json()
    except requests.RequestException as e:
        print(f"Request failed: {e}")
        return None


@st.cache_resource()
def get_model(model_name):
    """
    Load and cache model based on name with GPU support
    """
    if model_name == "ECCV16":
        model = eccv16(pretrained=True).eval()
    elif model_name == "SIGGRAPH17":
        model = siggraph17(pretrained=True).eval()
    else:
        raise ValueError(f"Unknown model: {model_name}")
    
    # Move to GPU if available
    if torch.cuda.is_available():
        model = model.cuda()
        st.info("🚀 GPU detected! Using GPU acceleration for faster processing.")
    else:
        st.info("💻 Using CPU processing. Consider using a GPU for faster results.")
    
    return model


def format_time(seconds: float) -> str:
    """Formats time in seconds to a human readable format"""
    if seconds < 60:
        return f"{int(seconds)} seconds"
    if seconds < 3600:
        minutes = seconds // 60
        seconds %= 60
        return f"{minutes} minutes and {int(seconds)} seconds"
    if seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds %= 60
        return f"{hours} hours, {minutes} minutes, and {int(seconds)} seconds"

    days = seconds // 86400
    hours = (seconds % 86400) // 3600
    minutes = (seconds % 3600) // 60
    seconds %= 60
    return f"{days} days, {hours} hours, {minutes} minutes, and {int(seconds)} seconds"


# Function to colorize video frames
def colorize_frame(frame, colorizer) -> np.ndarray:
    """
    Colorize frame
    """
    tens_l_orig, tens_l_rs = preprocess_img(frame, HW=(256, 256))
    return postprocess_tens(tens_l_orig, colorizer(tens_l_rs).cpu())


def colorize_frames_batch(frames, colorizer, batch_size=8):
    """
    Colorize multiple frames in batches for better performance
    """
    colorized_frames = []
    
    # Set model to evaluation mode and disable gradients for faster inference
    colorizer.eval()
    
    for i in range(0, len(frames), batch_size):
        batch = frames[i:i + batch_size]
        batch_tensors = []
        orig_tensors = []
        
        # Preprocess batch
        for frame in batch:
            tens_l_orig, tens_l_rs = preprocess_img(frame, HW=(256, 256))
            batch_tensors.append(tens_l_rs)
            orig_tensors.append(tens_l_orig)
        
        # Stack tensors for batch processing
        if batch_tensors:
            batch_tensor = torch.cat(batch_tensors, dim=0)
            
            # Process batch through model with optimizations
            with torch.no_grad():
                # Move to GPU if available for faster processing
                device = next(colorizer.parameters()).device
                batch_tensor = batch_tensor.to(device)
                batch_output = colorizer(batch_tensor).cpu()
            
            # Postprocess each frame in batch
            for j, (tens_l_orig, out_ab) in enumerate(zip(orig_tensors, batch_output)):
                colorized = postprocess_tens(tens_l_orig, out_ab.unsqueeze(0))
                colorized_frames.append((colorized * 255).astype(np.uint8))
    
    return colorized_frames


def get_video_info(video_path):
    """
    Get video information efficiently
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return None
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    duration = frame_count / fps if fps > 0 else 0
    
    cap.release()
    
    return {
        'fps': fps,
        'frame_count': frame_count,
        'width': width,
        'height': height,
        'duration': duration
    }


def colorize_image(file, loaded_model):
    """
    Colorize image
    """
    img = load_img(file)
    # If user input a colored image with 4 channels, discard the fourth channel
    if img.shape[2] == 4:
        img = img[:, :, :3]

    tens_l_orig, tens_l_rs = preprocess_img(img, HW=(256, 256))
    out_img = postprocess_tens(tens_l_orig, loaded_model(tens_l_rs).cpu())
    new_img = Image.fromarray((out_img * 255).astype(np.uint8))

    return out_img, new_img
