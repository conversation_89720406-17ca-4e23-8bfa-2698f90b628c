import os
import tempfile
import time
import uuid

import cv2
import moviepy.editor as mp
import numpy as np
import streamlit as st

from tqdm import tqdm

from utils import format_time, colorize_frame, colorize_frames_batch, get_model, setup_columns, set_page_config, clear_model_cache, check_model_files

set_page_config()
col2 = setup_columns()

with col2:
    st.write(
        """
    ## B&W Videos Colorizer
    ##### Upload a black and white video and get a colorized version of it.
    ###### ➠ This space is using CPU Basic so it might take a while to colorize a video.
    ###### ➠ If you want more models and GPU available please support this space by donating."""
    )


def main():
    """
    Main function to run this page
    """
    model = st.selectbox(
        "Select Model (Both models have their pros and cons, I recommend trying both and keeping the best for your "
        "task)",
        ["ECCV16", "SIGGRAPH17"],
        index=0,
    )

    # Add troubleshooting section
    with st.expander("🔧 Troubleshooting", expanded=False):
        st.write("If you're experiencing issues with the colorization:")
        col_a, col_b = st.columns(2)
        with col_a:
            if st.button("Clear Model Cache"):
                clear_model_cache()
        with col_b:
            if st.button("Check Model Files"):
                check_model_files()

        st.write("**Common Issues:**")
        st.write("- 'index -100001 is out of bounds' error: Usually indicates model loading issues")
        st.write("- Try clearing the cache and reloading the page")
        st.write("- Ensure you have a stable internet connection for model downloads")

    try:
        loaded_model = get_model(model)
        st.write(f"✅ Model {model} loaded successfully")
    except Exception as e:
        st.error(f"❌ Error loading model: {e}")
        st.error("Please try the troubleshooting options above.")
        return

    # Performance options
    col1, col2 = st.columns(2)
    with col1:
        processing_quality = st.selectbox(
            "Processing Quality (affects speed)",
            ["Fast (Lower Quality)", "Balanced", "High Quality (Slower)"],
            index=1
        )
    with col2:
        max_duration = st.slider(
            "Max video duration to process (minutes)", 
            min_value=0.5, 
            max_value=10.0, 
            value=3.0, 
            step=0.5,
            help="Longer videos will be processed with frame skipping"
        )

    uploaded_file = st.file_uploader("Upload your video here...", type=["mp4", "mov", "avi", "mkv"])

    if st.button("Colorize"):
        if uploaded_file is not None:
            file_extension = os.path.splitext(uploaded_file.name)[1].lower()
            if file_extension in [".mp4", ".avi", ".mov", ".mkv"]:
                # Generate unique filenames to avoid conflicts
                unique_id = str(uuid.uuid4())[:8]
                temp_file = None
                audio = None
                video = None
                clip = None
                
                try:
                    # Save the video file to a temporary location
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_extension)
                    temp_file.write(uploaded_file.read())
                    temp_file.close()

                    audio = mp.AudioFileClip(temp_file.name)

                    # Open the video using cv2.VideoCapture
                    video = cv2.VideoCapture(temp_file.name)
                    
                    if not video.isOpened():
                        st.error("Error: Could not open video file. Please check if the file is valid.")
                        return

                    # Get video information
                    fps = video.get(cv2.CAP_PROP_FPS)
                    total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
                    duration = total_frames / fps if fps > 0 else 0
                    
                    if total_frames == 0:
                        st.error("Error: Video has no frames or is corrupted.")
                        return
                    
                    # Calculate frame skip for long videos
                    max_duration_seconds = max_duration * 60
                    frame_skip = 1
                    if duration > max_duration_seconds:
                        frame_skip = max(1, int(duration / max_duration_seconds))
                        st.info(f"Video is {duration:.1f}s long. Processing every {frame_skip} frame(s) to stay within {max_duration:.1f} minute limit.")
                        total_frames = total_frames // frame_skip
                    
                    # Adjust batch size based on quality setting
                    if processing_quality == "Fast (Lower Quality)":
                        batch_size = 16
                        chunk_size = 32
                    elif processing_quality == "Balanced":
                        batch_size = 8
                        chunk_size = 16
                    else:  # High Quality
                        batch_size = 4
                        chunk_size = 8

                    col1, col2 = st.columns([0.5, 0.5])
                    with col1:
                        st.markdown('<p style="text-align: center;">Before</p>', unsafe_allow_html=True)
                        st.video(temp_file.name)

                    with col2:
                        st.markdown('<p style="text-align: center;">After</p>', unsafe_allow_html=True)

                        with st.spinner("Processing video..."):
                            # Process video in chunks to save memory
                            output_filename = f"output_{unique_id}.mp4"
                            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
                            
                            # Initialize video writer (we'll get frame size from first frame)
                            out = None
                            
                            progress_bar = st.progress(0)
                            start_time = time.time()
                            time_text = st.text("Time Remaining: ")
                            
                            frames_processed = 0
                            frame_buffer = []
                            frame_counter = 0
                            
                            # Reset video capture to beginning
                            video.set(cv2.CAP_PROP_POS_FRAMES, 0)
                            
                            while frames_processed < total_frames:
                                # Read chunk of frames with frame skipping
                                frame_buffer = []
                                for _ in range(min(chunk_size, total_frames - frames_processed)):
                                    ret, frame = video.read()
                                    if not ret:
                                        break
                                    
                                    # Apply frame skipping
                                    if frame_counter % frame_skip == 0:
                                        frame_buffer.append(frame)
                                    frame_counter += 1
                                
                                if not frame_buffer:
                                    break
                                
                                # Process chunk with batch colorization
                                try:
                                    colorized_chunk = colorize_frames_batch(frame_buffer, loaded_model, batch_size=batch_size)
                                    if not colorized_chunk:
                                        st.warning(f"No frames were colorized in chunk starting at frame {frames_processed}")
                                        break
                                except Exception as e:
                                    st.error(f"Error processing frames {frames_processed}-{frames_processed + len(frame_buffer)}: {str(e)}")
                                    st.error("This might be the 'index -100001' error. Try using a different model or clearing the cache.")
                                    break
                                
                                # Initialize video writer with first frame dimensions
                                if out is None and colorized_chunk:
                                    frame_size = colorized_chunk[0].shape[:2]
                                    out = cv2.VideoWriter(output_filename, fourcc, fps, (frame_size[1], frame_size[0]))
                                
                                # Write colorized frames to output video
                                for colorized_frame in colorized_chunk:
                                    frame_bgr = cv2.cvtColor(colorized_frame, cv2.COLOR_RGB2BGR)
                                    out.write(frame_bgr)
                                
                                frames_processed += len(colorized_chunk)
                                
                                # Update progress
                                elapsed_time = time.time() - start_time
                                if frames_processed > 0:
                                    time_remaining = ((total_frames - frames_processed) / frames_processed) * elapsed_time
                                    progress_bar.progress(frames_processed / total_frames)
                                    if frames_processed < total_frames:
                                        time_text.text(f"Time Remaining: {format_time(time_remaining)}")
                            
                            if out is not None:
                                out.release()
                            else:
                                st.error("No frames were processed successfully.")
                                return
                                
                            progress_bar.empty()
                            time_text.empty()

                            # Convert the output video to a format compatible with Streamlit
                            converted_filename = f"converted_output_{unique_id}.mp4"
                            clip = mp.VideoFileClip(output_filename)
                            clip = clip.set_audio(audio)

                            clip.write_videofile(converted_filename, codec="libx264")

                            # Display the converted video using st.video()
                            st.video(converted_filename)
                            st.balloons()

                            # Add a download button for the colorized video
                            with open(converted_filename, "rb") as f:
                                st.download_button(
                                    label="Download Colorized Video",
                                    data=f.read(),
                                    file_name="colorized_video.mp4",
                                )

                except Exception as e:
                    st.error(f"An error occurred during processing: {e}")
                
                finally:
                    # Clean up resources
                    if video is not None:
                        video.release()
                    if audio is not None:
                        audio.close()
                    if clip is not None:
                        clip.close()
                    
                    # Clean up temporary files
                    if temp_file is not None:
                        try:
                            os.unlink(temp_file.name)
                        except OSError:
                            pass
                    
                    # Clean up generated files
                    for filename in [f"output_{unique_id}.mp4", f"converted_output_{unique_id}.mp4"]:
                        try:
                            if os.path.exists(filename):
                                os.unlink(filename)
                        except OSError:
                            pass
            else:
                st.error("Please upload a valid video file (mp4, mov, avi, mkv)")
        else:
            st.warning("Please upload a video file first")


if __name__ == "__main__":
    main()
    st.markdown(
        "###### Made with :heart: by [Clément Delteil](https://www.linkedin.com/in/clementdelteil/) [![this is an "
        "image link](https://i.imgur.com/thJhzOO.png)](https://www.buymeacoffee.com/clementdelteil)"
    )
