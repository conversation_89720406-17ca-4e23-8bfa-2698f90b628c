repos:
  - repo: https://github.com/psf/black
    rev: 24.4.0
    hooks:
      - id: black
        args: ['--line-length=120', '--verbose']
        exclude: '^models/'

  - repo: https://github.com/pycqa/flake8
    rev: '7.0.0'
    hooks:
      - id: flake8
        exclude: '^models/'

  - repo: https://github.com/pre-commit/mirrors-pylint
    rev: v3.0.0a5
    hooks:
      - id: pylint
        name: pylint
        entry: pylint
        language: system
        args: ['.', '--rcfile=setup.cfg', '--fail-under=8']
        exclude: '^models/'
        types: [python]